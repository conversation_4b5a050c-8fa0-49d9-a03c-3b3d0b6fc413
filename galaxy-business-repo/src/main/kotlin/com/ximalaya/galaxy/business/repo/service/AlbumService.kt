package com.ximalaya.galaxy.business.repo.service

import com.baomidou.mybatisplus.extension.service.IService
import com.ximalaya.galaxy.business.repo.entity.AlbumEntity

/**
 *<AUTHOR>
 *@create 2025-06-02 10:03
 */
interface AlbumService : IService<AlbumEntity> {

    fun selectBySessionCode(sessionCode: Long): AlbumEntity?

    fun selectByUid(uid: Long): List<AlbumEntity>

    fun selectByUidAndSessionCode(uid: Long, sessionCode: Long): AlbumEntity?

    fun createAlbum(uid: Long, sessionCode: Long, albumName: String, outline: String? = null): Long

    fun updateAlbum(id: Long, albumName: String? = null, outline: String? = null): Boolean

    fun deleteAlbum(id: Long): Boolean

}

package com.ximalaya.galaxy.business.repo.service.impl

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl
import com.ximalaya.galaxy.business.common.enums.ErrorCode
import com.ximalaya.galaxy.business.common.support.GalaxyAsserts
import com.ximalaya.galaxy.business.repo.entity.AlbumEntity
import com.ximalaya.galaxy.business.repo.enums.LogicDeleted
import com.ximalaya.galaxy.business.repo.mapper.AlbumMapper
import com.ximalaya.galaxy.business.repo.service.AlbumService
import org.apache.commons.lang3.StringUtils
import org.springframework.stereotype.Repository
import java.time.LocalDateTime

/**
 *<AUTHOR>
 *@create 2025-06-02 10:03
 */
@Repository
class AlbumServiceImpl : ServiceImpl<AlbumMapper, AlbumEntity>(), AlbumService {

    override fun selectBySessionCode(sessionCode: Long): AlbumEntity? {
        return ktQuery().eq(AlbumEntity::sessionCode, sessionCode)
            .eq(AlbumEntity::deletedAt, LogicDeleted.SAVE.getCode())
            .one()
    }

    override fun selectByUid(uid: Long): List<AlbumEntity> {
        return ktQuery().eq(AlbumEntity::uid, uid)
            .eq(AlbumEntity::deletedAt, LogicDeleted.SAVE.getCode())
            .orderByDesc(AlbumEntity::createTime)
            .list()
    }

    override fun selectByUidAndSessionCode(uid: Long, sessionCode: Long): AlbumEntity? {
        return ktQuery().eq(AlbumEntity::uid, uid)
            .eq(AlbumEntity::sessionCode, sessionCode)
            .eq(AlbumEntity::deletedAt, LogicDeleted.SAVE.getCode())
            .one()
    }

    override fun createAlbum(uid: Long, sessionCode: Long, albumName: String, outline: String?): Long {
        val now = LocalDateTime.now()

        val albumEntity = AlbumEntity().apply {
            this.uid = uid
            this.sessionCode = sessionCode
            this.albumName = albumName
            this.outline = outline
            this.deletedAt = LogicDeleted.SAVE.getCode()
            this.createTime = now
            this.updateTime = now
            this.dataCreateTime = now
            this.dataUpdateTime = now
        }

        GalaxyAsserts.assertTrue(save(albumEntity), ErrorCode.UNKNOWN_ERROR, "创建专辑失败")
        return albumEntity.id!!
    }

    override fun updateAlbum(id: Long, albumName: String?, outline: String?): Boolean {
        val updateWrapper = ktUpdate().eq(AlbumEntity::id, id)
            .eq(AlbumEntity::deletedAt, LogicDeleted.SAVE.getCode())
            .set(AlbumEntity::updateTime, LocalDateTime.now())
            .set(AlbumEntity::dataUpdateTime, LocalDateTime.now())

        if (StringUtils.isNotBlank(albumName)) {
            updateWrapper.set(AlbumEntity::albumName, albumName)
        }

        if (StringUtils.isNotBlank(outline)) {
            updateWrapper.set(AlbumEntity::outline, outline)
        }

        return updateWrapper.update()
    }

    override fun deleteAlbum(id: Long): Boolean {
        return ktUpdate().eq(AlbumEntity::id, id)
            .eq(AlbumEntity::deletedAt, LogicDeleted.SAVE.getCode())
            .set(AlbumEntity::deletedAt, LogicDeleted.DELETED.getCode())
            .set(AlbumEntity::updateTime, LocalDateTime.now())
            .update()
    }

}

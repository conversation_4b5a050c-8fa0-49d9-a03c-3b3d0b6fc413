package com.ximalaya.galaxy.business.business.boss

import com.ximalaya.galaxy.business.business.boss.vo.SessionPhasesVo
import com.ximalaya.galaxy.business.business.boss.vo.SessionVo
import com.ximalaya.galaxy.business.business.boss.vo.StartSessionJobDto
import com.ximalaya.galaxy.business.business.boss.vo.StartToolJobDto
import com.ximalaya.galaxy.business.repo.dto.GalaxyPage
import com.ximalaya.galaxy.business.repo.enums.PhaseState
import com.ximalaya.galaxy.business.repo.enums.SessionState
import com.ximalaya.galaxy.business.service.vo.AgentContentProtocol
import com.ximalaya.galaxy.business.vo.QueryPageRequestVo
import org.springframework.web.servlet.mvc.method.annotation.SseEmitter

/**
 *<AUTHOR>
 *@create 2025-05-20 10:52
 */
interface BizGalaxyBoss {

    fun createSessionAndFirstPhase(uid: Long, sessionName: String): Pair<Long, Long>

    fun createSession(uid: Long, sessionName: String): Long

    fun updateSessionState(sessionCode: Long, sessionState: SessionState): Boolean

    fun createPhase(uid: Long, sessionCode: Long, phaseName: String): Long

    fun updatePhaseState(sessionCode: Long, phaseCode: Long, phaseState: PhaseState): Boolean

    fun getSessionPhases(uid: Long, sessionCode: Long): SessionPhasesVo

    fun connectSessionPhase(emitter: SseEmitter, uid: Long, sessionCode: Long, phaseCode: Long)

    fun startSessionJob(emitter: SseEmitter, startVo: StartSessionJobDto)

    fun startToolJob(emitter: SseEmitter, startVo: StartToolJobDto)

    fun sendSseWriteBackMessage(agentMessage: AgentContentProtocol)

    fun updateBlockContent(sessionCode: Long, phaseCode: Long, blockCode: Long, content: String): Boolean

    fun getHistoricalSessions(uid: Long, pageRequest: QueryPageRequestVo): GalaxyPage<SessionVo>

}
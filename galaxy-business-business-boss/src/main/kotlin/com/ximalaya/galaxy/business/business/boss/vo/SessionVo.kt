package com.ximalaya.galaxy.business.business.boss.vo

import com.ximalaya.galaxy.business.repo.entity.GalaxySessionEntity
import com.ximalaya.galaxy.business.repo.enums.SessionState

/**
 *<AUTHOR>
 *@create 2025-05-22 21:55
 */
class SessionVo {

    var sessionCode: Long? = null

    var sessionName: String? = null

    var uid: Long? = null

    var sessionState: SessionState? = null

    companion object {

        fun of(sessionEntity: GalaxySessionEntity): SessionVo {
            return SessionVo().apply {
                this.sessionCode = sessionEntity.sessionCode
                this.sessionName = sessionEntity.sessionName
                this.uid = sessionEntity.uid
                this.sessionState = SessionState.parseCode(sessionEntity.sessionState)
            }
        }

    }

}
package com.ximalaya.galaxy.business.business.boss.vo

import com.ximalaya.galaxy.business.repo.entity.GalaxyPhaseEntity
import com.ximalaya.galaxy.business.repo.entity.GalaxySessionEntity
import com.ximalaya.galaxy.business.repo.enums.PhaseState
import com.ximalaya.galaxy.business.repo.enums.SessionState

/**
 *<AUTHOR>
 *@create 2025-05-22 21:55
 */
class SessionPhasesVo {

  var sessionCode: Long? = null

  var sessionName: String? = null

  var uid: Long? = null

  var sessionState: SessionState? = null

  var phases: List<PhaseVo>? = null

  companion object {

    fun of(sessionEntity: GalaxySessionEntity, phaseEntities: List<GalaxyPhaseEntity>?): SessionPhasesVo {
      return SessionPhasesVo().apply {
        this.sessionCode = sessionEntity.sessionCode
        this.sessionName = sessionEntity.sessionName
        this.uid = sessionEntity.uid
        this.sessionState = SessionState.parseCode(sessionEntity.sessionState)
        this.phases = phaseEntities?.map { PhaseVo.of(it) }
      }
    }

  }

}

class PhaseVo {

  var phaseCode: Long? = null

  var phaseName: String? = null

  var phaseState: PhaseState? = null

  companion object {

    fun of(phaseEntity: GalaxyPhaseEntity): PhaseVo {
      return PhaseVo().apply {
        this.phaseCode = phaseEntity.phaseCode
        this.phaseName = phaseEntity.phaseName
        this.phaseState = PhaseState.parseCode(phaseEntity.phaseState)
      }
    }

  }

}
package com.ximalaya.galaxy.business.vo

import com.alibaba.fastjson.JSON
import com.baomidou.mybatisplus.extension.plugins.pagination.Page
import io.swagger.annotations.ApiModel
import io.swagger.annotations.ApiModelProperty

/**
 *<AUTHOR>
 *@create 2023-08-06 19:29
 */
@ApiModel(description = "分页信息")
open class QueryPageRequestVo {

  @ApiModelProperty("分页-页码 从1开始")
  var pageNum: Long = 1

  @ApiModelProperty("分页-数量")
  var pageSize: Long = 10

  @ApiModelProperty(hidden = true)
  fun <T> toPageInfo() = Page<T>(pageNum, pageSize)

  @ApiModelProperty(hidden = true)
  fun getOffset() = (pageNum - 1) * pageSize

  @ApiModelProperty(hidden = true)
  fun getLimit() = pageSize

  @ApiModelProperty(hidden = true)
  fun getStartIndex() = (pageNum - 1) * pageSize

  @ApiModelProperty(hidden = true)
  fun getEndIndex() = getStartIndex() + pageSize - 1

  override fun toString() =
    "QueryPageRequestVo(${JSON.toJSONString(this)})"

}